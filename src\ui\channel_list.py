"""
Channel list view for IPTV Player
"""

from kivy.clock import Clock
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDS<PERSON>rollView
from kivymd.uix.list import <PERSON><PERSON>ist, TwoLineAvatarIconListItem
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.button import MDIconButton, MDFlatButton
from kivymd.uix.chip import MDChip
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.imagelist import MDSmartTile
from kivy.metrics import dp

from ..core.logger import LoggerMixin
from ..models.channel import Channel
from ..models.user_preferences import ViewMode, SortOrder


class ChannelListView(MDBoxLayout, LoggerMixin):
    """View for displaying channel list"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.channels = []
        self.filtered_channels = []
        self.current_playlist = None
        self.current_category = None
        self.view_mode = ViewMode.LIST
        self.sort_order = SortOrder.NAME_ASC
        self.show_favorites_only = False
        self.show_recent_only = False
        
        self.build_ui()
    
    def build_ui(self):
        """Build the channel list UI"""
        # Filter and sort toolbar
        filter_toolbar = self.create_filter_toolbar()
        self.add_widget(filter_toolbar)
        
        # Category chips
        self.category_scroll = MDScrollView(
            size_hint_y=None,
            height=dp(60)
        )
        self.category_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8),
            padding=[dp(16), dp(8)],
            size_hint_x=None,
            adaptive_width=True
        )
        self.category_scroll.add_widget(self.category_layout)
        self.add_widget(self.category_scroll)
        
        # Channel content area
        self.content_scroll = MDScrollView()
        self.channel_container = MDBoxLayout(orientation='vertical')
        self.content_scroll.add_widget(self.channel_container)
        self.add_widget(self.content_scroll)
        
        # Initially show empty state
        self.show_empty_state()
    
    def create_filter_toolbar(self):
        """Create filter and sort toolbar"""
        toolbar = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=[dp(16), dp(8)],
            spacing=dp(8)
        )
        
        # View mode toggle
        self.view_mode_button = MDIconButton(
            icon="view-list",
            on_release=self.toggle_view_mode
        )
        toolbar.add_widget(self.view_mode_button)
        
        # Sort button
        self.sort_button = MDFlatButton(
            text="Name ↑",
            on_release=self.show_sort_menu
        )
        toolbar.add_widget(self.sort_button)
        
        # Spacer
        toolbar.add_widget(MDLabel())
        
        # Favorites filter
        self.favorites_button = MDIconButton(
            icon="heart-outline",
            on_release=self.toggle_favorites_filter
        )
        toolbar.add_widget(self.favorites_button)
        
        # Search button
        search_button = MDIconButton(
            icon="magnify",
            on_release=self.show_search
        )
        toolbar.add_widget(search_button)
        
        return toolbar
    
    def load_playlist(self, playlist):
        """Load channels from playlist"""
        self.current_playlist = playlist
        self.logger.info(f"Loading playlist: {playlist.name}")
        
        # TODO: Load channels from database
        self.channels = []
        self.update_categories()
        self.filter_and_sort_channels()
    
    def update_categories(self):
        """Update category chips"""
        self.category_layout.clear_widgets()
        
        if not self.channels:
            return
        
        # Get unique categories
        categories = set()
        for channel in self.channels:
            category = channel.get_category_display()
            categories.add(category)
        
        # Add "All" chip
        all_chip = MDChip(
            text="All",
            selected=self.current_category is None,
            on_release=lambda x: self.select_category(None)
        )
        self.category_layout.add_widget(all_chip)
        
        # Add category chips
        for category in sorted(categories):
            chip = MDChip(
                text=category,
                selected=self.current_category == category,
                on_release=lambda x, cat=category: self.select_category(cat)
            )
            self.category_layout.add_widget(chip)
    
    def select_category(self, category):
        """Select a category filter"""
        self.current_category = category
        self.update_category_selection()
        self.filter_and_sort_channels()
    
    def update_category_selection(self):
        """Update category chip selection"""
        for chip in self.category_layout.children:
            if isinstance(chip, MDChip):
                if chip.text == "All":
                    chip.selected = self.current_category is None
                else:
                    chip.selected = chip.text == self.current_category
    
    def filter_and_sort_channels(self):
        """Filter and sort channels based on current settings"""
        # Start with all channels
        filtered = self.channels.copy()
        
        # Apply category filter
        if self.current_category:
            filtered = [ch for ch in filtered if ch.get_category_display() == self.current_category]
        
        # Apply favorites filter
        if self.show_favorites_only:
            filtered = [ch for ch in filtered if ch.is_favorite]
        
        # Apply hidden filter
        filtered = [ch for ch in filtered if not ch.is_hidden]
        
        # Sort channels
        if self.sort_order == SortOrder.NAME_ASC:
            filtered.sort(key=lambda ch: ch.name.lower())
        elif self.sort_order == SortOrder.NAME_DESC:
            filtered.sort(key=lambda ch: ch.name.lower(), reverse=True)
        elif self.sort_order == SortOrder.GROUP_ASC:
            filtered.sort(key=lambda ch: (ch.group or "").lower())
        elif self.sort_order == SortOrder.GROUP_DESC:
            filtered.sort(key=lambda ch: (ch.group or "").lower(), reverse=True)
        elif self.sort_order == SortOrder.FAVORITES_FIRST:
            filtered.sort(key=lambda ch: (not ch.is_favorite, ch.name.lower()))
        
        self.filtered_channels = filtered
        self.update_channel_display()
    
    def update_channel_display(self):
        """Update channel display based on view mode"""
        self.channel_container.clear_widgets()
        
        if not self.filtered_channels:
            self.show_empty_state()
            return
        
        if self.view_mode == ViewMode.LIST:
            self.show_list_view()
        elif self.view_mode == ViewMode.GRID:
            self.show_grid_view()
        else:
            self.show_compact_view()
    
    def show_list_view(self):
        """Show channels in list view"""
        channel_list = MDList()
        
        for channel in self.filtered_channels:
            item = TwoLineAvatarIconListItem(
                text=channel.name,
                secondary_text=channel.get_category_display(),
                on_release=lambda x, ch=channel: self.play_channel(ch)
            )
            
            # Channel logo or icon
            if channel.logo:
                # TODO: Load channel logo
                pass
            
            # Favorite button
            fav_button = MDIconButton(
                icon="heart" if channel.is_favorite else "heart-outline",
                theme_icon_color="Custom",
                icon_color=self.theme_cls.accent_color if channel.is_favorite else self.theme_cls.primary_color,
                on_release=lambda x, ch=channel: self.toggle_favorite(ch)
            )
            item.add_widget(fav_button)
            
            channel_list.add_widget(item)
        
        self.channel_container.add_widget(channel_list)
    
    def show_grid_view(self):
        """Show channels in grid view"""
        grid = MDGridLayout(
            cols=3,
            spacing=dp(8),
            padding=dp(16),
            size_hint_y=None,
            adaptive_height=True
        )
        
        for channel in self.filtered_channels:
            card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                height=dp(200),
                elevation=2,
                on_release=lambda x, ch=channel: self.play_channel(ch)
            )
            
            # Channel logo area
            if channel.logo:
                # TODO: Add channel logo
                pass
            else:
                logo_placeholder = MDLabel(
                    text=channel.name[:2].upper(),
                    halign="center",
                    theme_text_color="Primary",
                    font_style="H4"
                )
                card.add_widget(logo_placeholder)
            
            # Channel name
            name_label = MDLabel(
                text=channel.name,
                halign="center",
                theme_text_color="Primary",
                font_style="Caption",
                size_hint_y=None,
                height=dp(40)
            )
            card.add_widget(name_label)
            
            grid.add_widget(card)
        
        self.channel_container.add_widget(grid)
    
    def show_compact_view(self):
        """Show channels in compact view"""
        # Similar to list view but more compact
        self.show_list_view()
    
    def show_empty_state(self):
        """Show empty state when no channels"""
        empty_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint=(None, None),
            size=(dp(300), dp(200)),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        empty_icon = MDLabel(
            text="📺",
            halign="center",
            font_size="48sp"
        )
        
        if self.show_favorites_only:
            empty_text = "No favorite channels found"
        elif self.current_category:
            empty_text = f"No channels in '{self.current_category}' category"
        else:
            empty_text = "No channels available\nAdd a playlist to get started"
        
        empty_label = MDLabel(
            text=empty_text,
            halign="center",
            theme_text_color="Secondary"
        )
        
        empty_layout.add_widget(empty_icon)
        empty_layout.add_widget(empty_label)
        self.channel_container.add_widget(empty_layout)
    
    def toggle_view_mode(self, *args):
        """Toggle between view modes"""
        if self.view_mode == ViewMode.LIST:
            self.view_mode = ViewMode.GRID
            self.view_mode_button.icon = "view-grid"
        elif self.view_mode == ViewMode.GRID:
            self.view_mode = ViewMode.COMPACT
            self.view_mode_button.icon = "view-compact"
        else:
            self.view_mode = ViewMode.LIST
            self.view_mode_button.icon = "view-list"
        
        self.update_channel_display()
    
    def show_sort_menu(self, *args):
        """Show sort options menu"""
        menu_items = [
            {"text": "Name ↑", "on_release": lambda: self.set_sort_order(SortOrder.NAME_ASC)},
            {"text": "Name ↓", "on_release": lambda: self.set_sort_order(SortOrder.NAME_DESC)},
            {"text": "Category ↑", "on_release": lambda: self.set_sort_order(SortOrder.GROUP_ASC)},
            {"text": "Category ↓", "on_release": lambda: self.set_sort_order(SortOrder.GROUP_DESC)},
            {"text": "Favorites First", "on_release": lambda: self.set_sort_order(SortOrder.FAVORITES_FIRST)},
        ]
        
        self.sort_menu = MDDropdownMenu(
            caller=self.sort_button,
            items=menu_items,
            width_mult=4
        )
        self.sort_menu.open()
    
    def set_sort_order(self, sort_order):
        """Set sort order"""
        self.sort_order = sort_order
        
        # Update button text
        sort_text = {
            SortOrder.NAME_ASC: "Name ↑",
            SortOrder.NAME_DESC: "Name ↓",
            SortOrder.GROUP_ASC: "Category ↑",
            SortOrder.GROUP_DESC: "Category ↓",
            SortOrder.FAVORITES_FIRST: "Favorites First"
        }
        self.sort_button.text = sort_text.get(sort_order, "Name ↑")
        
        self.sort_menu.dismiss()
        self.filter_and_sort_channels()
    
    def toggle_favorites_filter(self, *args):
        """Toggle favorites filter"""
        self.show_favorites_only = not self.show_favorites_only
        self.favorites_button.icon = "heart" if self.show_favorites_only else "heart-outline"
        self.filter_and_sort_channels()
    
    def show_favorites(self):
        """Show only favorite channels"""
        self.show_favorites_only = True
        self.favorites_button.icon = "heart"
        self.filter_and_sort_channels()
    
    def show_recent(self):
        """Show recently watched channels"""
        self.show_recent_only = True
        # TODO: Implement recent channels logic
        self.filter_and_sort_channels()
    
    def show_search(self, *args):
        """Show search interface"""
        # TODO: Implement search
        self.logger.info("Search requested")
    
    def play_channel(self, channel):
        """Play selected channel"""
        self.logger.info(f"Playing channel: {channel.name}")
        # TODO: Implement video player
    
    def toggle_favorite(self, channel):
        """Toggle channel favorite status"""
        channel.is_favorite = not channel.is_favorite
        self.logger.info(f"Toggled favorite for {channel.name}: {channel.is_favorite}")
        # TODO: Update in database
        self.update_channel_display()
