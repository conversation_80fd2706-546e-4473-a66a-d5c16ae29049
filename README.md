# IPTV Player

A cross-platform IPTV application built with Python and Kivy/KivyMD that supports multiple playlist formats and content types.

## Features

### Playlist Formats
- M3U and M3U8 playlists
- Xtream Codes API (username, password, URL)
- Stalker Portal using MAC Address authentication (MAG/STB Emulator)

### Content Types
- Live TV with EPG support
- VOD (Video on Demand)
- Series with season and episode navigation

### UI/UX Features
- Modern, responsive user interface
- Category organization (Sports, News, Movies, Series, Kids)
- Favorites list
- Channel locking with PIN
- Show/hide channels or categories
- Sorting and search functionality
- Playback position memory for VOD/Series
- Customizable player with VLC backend

### Backend Features
- M3U/M3U8 parsing and display
- Xtream API authentication and playlist fetching
- MAG device simulation with MAC address
- Local SQLite storage for preferences
- Channel logos and program info display
- Local caching for performance
- Parental control settings

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd iptv-player
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python main.py
```

## Project Structure

```
iptv-player/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── src/
│   ├── core/              # Core application logic
│   ├── models/            # Data models
│   ├── parsers/           # Playlist parsers
│   ├── providers/         # Content providers
│   ├── ui/                # User interface components
│   └── utils/             # Utility functions
└── tests/                 # Unit tests
```

## Configuration

The application stores configuration and data in:
- Windows: `%USERPROFILE%\.iptv_player\`
- Linux/macOS: `~/.iptv_player/`

## License

This project is licensed under the MIT License.
