"""
EPG (Electronic Program Guide) view for IPTV Player
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional
from kivy.clock import Clock
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.button import MDIconButton, MDFlatButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.dialog import MDDialog
from kivymd.uix.progressbar import MDProgressBar
from kivy.metrics import dp

from ..core.logger import LoggerMixin
from ..models.channel import Channel
from ..models.epg import EPGProgram, EPGChannel, EPGData


class EPGTimelineView(MDScrollView, LoggerMixin):
    """Timeline view for EPG programs"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.channels = []
        self.epg_data = EPGData()
        self.current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        self.time_slots = []
        self.channel_height = dp(60)
        self.time_slot_width = dp(120)
        
        self.build_ui()
    
    def build_ui(self):
        """Build the EPG timeline UI"""
        self.main_layout = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            adaptive_height=True
        )
        
        # Time header
        self.time_header = self.create_time_header()
        self.main_layout.add_widget(self.time_header)
        
        # Channel grid
        self.channel_grid = MDGridLayout(
            cols=1,
            size_hint_y=None,
            adaptive_height=True,
            spacing=dp(1)
        )
        self.main_layout.add_widget(self.channel_grid)
        
        self.add_widget(self.main_layout)
    
    def create_time_header(self):
        """Create time header with hour slots"""
        header = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(40),
            size_hint_x=None,
            adaptive_width=True
        )
        
        # Channel name column header
        channel_header = MDCard(
            size_hint=(None, 1),
            width=dp(150),
            md_bg_color=self.theme_cls.primary_color
        )
        channel_header.add_widget(MDLabel(
            text="Channels",
            halign="center",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="Subtitle1"
        ))
        header.add_widget(channel_header)
        
        # Time slots
        self.time_slots = []
        start_time = self.current_date
        
        for hour in range(24):
            time_slot = start_time + timedelta(hours=hour)
            self.time_slots.append(time_slot)
            
            time_card = MDCard(
                size_hint=(None, 1),
                width=self.time_slot_width,
                md_bg_color=self.theme_cls.primary_light
            )
            time_card.add_widget(MDLabel(
                text=time_slot.strftime("%H:%M"),
                halign="center",
                theme_text_color="Primary",
                font_style="Caption"
            ))
            header.add_widget(time_card)
        
        return header
    
    def load_channels(self, channels: List[Channel]):
        """Load channels and their EPG data"""
        self.channels = channels
        self.refresh_epg_display()
    
    def refresh_epg_display(self):
        """Refresh the EPG display"""
        self.channel_grid.clear_widgets()
        
        for channel in self.channels:
            channel_row = self.create_channel_row(channel)
            self.channel_grid.add_widget(channel_row)
    
    def create_channel_row(self, channel: Channel):
        """Create EPG row for a channel"""
        row = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=self.channel_height,
            size_hint_x=None,
            adaptive_width=True
        )
        
        # Channel info
        channel_info = self.create_channel_info_card(channel)
        row.add_widget(channel_info)
        
        # Program timeline
        timeline = self.create_program_timeline(channel)
        row.add_widget(timeline)
        
        return row
    
    def create_channel_info_card(self, channel: Channel):
        """Create channel information card"""
        card = MDCard(
            size_hint=(None, 1),
            width=dp(150),
            md_bg_color=self.theme_cls.surface_color,
            padding=dp(8)
        )
        
        info_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8)
        )
        
        # Channel logo placeholder
        logo_placeholder = MDLabel(
            text=channel.name[:2].upper(),
            size_hint_x=None,
            width=dp(40),
            halign="center",
            theme_text_color="Primary",
            font_style="H6"
        )
        info_layout.add_widget(logo_placeholder)
        
        # Channel name
        name_label = MDLabel(
            text=channel.name,
            theme_text_color="Primary",
            font_style="Body2"
        )
        info_layout.add_widget(name_label)
        
        card.add_widget(info_layout)
        return card
    
    def create_program_timeline(self, channel: Channel):
        """Create program timeline for a channel"""
        timeline = MDBoxLayout(
            orientation='horizontal',
            size_hint_x=None,
            adaptive_width=True
        )
        
        # Get EPG data for channel
        epg_channel = self.epg_data.get_channel(channel.id)
        if not epg_channel:
            # No EPG data available
            for _ in range(24):
                empty_slot = MDCard(
                    size_hint=(None, 1),
                    width=self.time_slot_width,
                    md_bg_color=self.theme_cls.disabled_hint_text_color
                )
                empty_slot.add_widget(MDLabel(
                    text="No EPG",
                    halign="center",
                    theme_text_color="Hint",
                    font_style="Caption"
                ))
                timeline.add_widget(empty_slot)
            return timeline
        
        # Get programs for current date
        programs = epg_channel.get_programs_for_date(self.current_date)
        
        # Create program cards
        current_time = self.current_date
        program_index = 0
        
        for hour in range(24):
            hour_start = current_time + timedelta(hours=hour)
            hour_end = hour_start + timedelta(hours=1)
            
            # Find programs in this hour
            hour_programs = [
                p for p in programs
                if not (p.end_time <= hour_start or p.start_time >= hour_end)
            ]
            
            if hour_programs:
                # Show first program in this hour
                program = hour_programs[0]
                program_card = self.create_program_card(program, channel)
                timeline.add_widget(program_card)
            else:
                # Empty slot
                empty_slot = MDCard(
                    size_hint=(None, 1),
                    width=self.time_slot_width,
                    md_bg_color=self.theme_cls.disabled_hint_text_color
                )
                timeline.add_widget(empty_slot)
        
        return timeline
    
    def create_program_card(self, program: EPGProgram, channel: Channel):
        """Create program information card"""
        # Determine card color based on program status
        if program.is_current():
            bg_color = self.theme_cls.accent_color
            text_color = (1, 1, 1, 1)
        elif program.is_upcoming():
            bg_color = self.theme_cls.primary_light
            text_color = self.theme_cls.primary_color
        else:
            bg_color = self.theme_cls.surface_color
            text_color = self.theme_cls.on_surface_color
        
        card = MDCard(
            size_hint=(None, 1),
            width=self.time_slot_width,
            md_bg_color=bg_color,
            padding=dp(4),
            on_release=lambda x: self.show_program_details(program, channel)
        )
        
        content_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(2)
        )
        
        # Program title
        title_label = MDLabel(
            text=program.title,
            theme_text_color="Custom",
            text_color=text_color,
            font_style="Caption",
            halign="center"
        )
        content_layout.add_widget(title_label)
        
        # Time range
        time_label = MDLabel(
            text=program.get_formatted_time_range(),
            theme_text_color="Custom",
            text_color=text_color,
            font_style="Overline",
            halign="center"
        )
        content_layout.add_widget(time_label)
        
        # Progress bar for current program
        if program.is_current():
            progress = program.get_progress_percentage()
            progress_bar = MDProgressBar(
                value=progress,
                size_hint_y=None,
                height=dp(4)
            )
            content_layout.add_widget(progress_bar)
        
        card.add_widget(content_layout)
        return card
    
    def show_program_details(self, program: EPGProgram, channel: Channel):
        """Show detailed program information"""
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(12),
            size_hint_y=None,
            adaptive_height=True
        )
        
        # Program title and time
        title_label = MDLabel(
            text=program.title,
            theme_text_color="Primary",
            font_style="H6"
        )
        content.add_widget(title_label)
        
        time_label = MDLabel(
            text=f"{program.get_formatted_time_range()} • {program.duration_minutes} min",
            theme_text_color="Secondary",
            font_style="Body2"
        )
        content.add_widget(time_label)
        
        # Description
        if program.description:
            desc_label = MDLabel(
                text=program.description,
                theme_text_color="Primary",
                font_style="Body1"
            )
            content.add_widget(desc_label)
        
        # Additional info
        if program.genre or program.rating:
            info_text = []
            if program.genre:
                info_text.append(f"Genre: {program.genre}")
            if program.rating:
                info_text.append(f"Rating: {program.rating}")
            
            info_label = MDLabel(
                text=" • ".join(info_text),
                theme_text_color="Secondary",
                font_style="Caption"
            )
            content.add_widget(info_label)
        
        # Dialog buttons
        buttons = [
            MDFlatButton(
                text="CLOSE",
                on_release=lambda x: dialog.dismiss()
            )
        ]
        
        # Add watch button for current/upcoming programs
        if program.is_current() or program.is_upcoming():
            watch_btn = MDFlatButton(
                text="WATCH CHANNEL",
                on_release=lambda x: self.watch_channel(channel, dialog)
            )
            buttons.insert(0, watch_btn)
        
        dialog = MDDialog(
            title=f"{channel.name} - Program Details",
            type="custom",
            content_cls=content,
            buttons=buttons
        )
        dialog.open()
    
    def watch_channel(self, channel: Channel, dialog: MDDialog):
        """Start watching the channel"""
        dialog.dismiss()
        # TODO: Implement channel playback
        self.logger.info(f"Starting playback for channel: {channel.name}")
    
    def navigate_date(self, days_offset: int):
        """Navigate to different date"""
        self.current_date += timedelta(days=days_offset)
        self.time_header = self.create_time_header()
        self.refresh_epg_display()
    
    def set_date(self, date: datetime):
        """Set specific date"""
        self.current_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        self.time_header = self.create_time_header()
        self.refresh_epg_display()


class EPGView(MDBoxLayout, LoggerMixin):
    """Main EPG view with navigation and timeline"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.current_playlist = None
        self.build_ui()
    
    def build_ui(self):
        """Build the EPG view UI"""
        # Navigation toolbar
        nav_toolbar = MDTopAppBar(
            title="TV Guide",
            left_action_items=[
                ["chevron-left", lambda x: self.timeline.navigate_date(-1)],
                ["calendar-today", lambda x: self.show_date_picker()]
            ],
            right_action_items=[
                ["chevron-right", lambda x: self.timeline.navigate_date(1)],
                ["refresh", lambda x: self.refresh_epg()]
            ]
        )
        self.add_widget(nav_toolbar)
        
        # Date label
        self.date_label = MDLabel(
            text=datetime.now().strftime("%A, %B %d, %Y"),
            halign="center",
            theme_text_color="Primary",
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(40)
        )
        self.add_widget(self.date_label)
        
        # EPG Timeline
        self.timeline = EPGTimelineView()
        self.add_widget(self.timeline)
        
        # Update date label when timeline date changes
        Clock.schedule_interval(self.update_date_label, 1.0)
    
    def load_playlist(self, playlist):
        """Load EPG data for playlist"""
        self.current_playlist = playlist
        self.logger.info(f"Loading EPG for playlist: {playlist.name}")
        
        # TODO: Load channels and EPG data from database
        channels = []  # Load from database
        self.timeline.load_channels(channels)
    
    def show_date_picker(self):
        """Show date picker dialog"""
        # TODO: Implement date picker
        self.logger.info("Date picker requested")
    
    def refresh_epg(self):
        """Refresh EPG data"""
        if self.current_playlist:
            self.logger.info("Refreshing EPG data")
            # TODO: Fetch fresh EPG data
            self.timeline.refresh_epg_display()
    
    def update_date_label(self, dt):
        """Update date label"""
        self.date_label.text = self.timeline.current_date.strftime("%A, %B %d, %Y")
        return True
