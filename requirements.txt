# Core dependencies
kivy==2.3.1
kivymd==1.2.0
python-vlc==3.0.20123
requests==2.31.0
aiohttp==3.9.1
asyncio-mqtt==0.16.1

# Database (sqlite3 is built-in, no need to install)

# M3U parsing
m3u8==4.0.0

# XML parsing for EPG
lxml==5.1.0
beautifulsoup4==4.12.2

# Encryption and security
cryptography>=41.0.0
keyring==24.3.0

# Image handling (already installed with <PERSON><PERSON>)
# Pillow==10.1.0
requests-cache==1.1.1

# Configuration
pyyaml==6.0.1
python-dotenv==1.0.0

# Networking
urllib3==2.1.0
certifi==2023.11.17

# Date/time handling
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Cross-platform support
plyer==2.1.0
