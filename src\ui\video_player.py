"""
Video player component for IPTV Player
"""

import os
import time
from typing import Optional, Callable, Dict, Any
from kivy.clock import Clock
from kivy.event import EventDispatcher
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.floatlayout import MD<PERSON><PERSON><PERSON>ayout
from kivymd.uix.button import <PERSON><PERSON><PERSON><PERSON><PERSON>on, MDFlatButton
from kivymd.uix.slider import MDSlider
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.card import MD<PERSON>ard
from kivymd.uix.dialog import MDDialog
from kivy.metrics import dp

try:
    import vlc
    VLC_AVAILABLE = True
except ImportError:
    VLC_AVAILABLE = False

from ..core.logger import LoggerMixin
from ..models.channel import Channel
from ..models.content import ContentInfo
from ..models.user_preferences import PlayerSettings


class VideoPlayer(MDFloatLayout, LoggerMixin, EventDispatcher):
    """Video player widget with VLC backend"""
    
    __events__ = ('on_play', 'on_pause', 'on_stop', 'on_position_change', 'on_error')
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Player state
        self.vlc_instance = None
        self.vlc_player = None
        self.current_media = None
        self.is_playing = False
        self.is_fullscreen = False
        self.duration = 0
        self.position = 0
        self.volume = 80
        
        # Current content
        self.current_channel = None
        self.current_content = None
        self.last_position_save_time = 0
        
        # Settings
        self.player_settings = PlayerSettings()
        
        # UI update timer
        self.update_timer = None
        
        # Build UI
        self.build_player_ui()
        
        # Initialize VLC if available
        if VLC_AVAILABLE:
            self.initialize_vlc()
        else:
            self.show_error("VLC not available. Please install python-vlc.")
    
    def build_player_ui(self):
        """Build the video player UI"""
        # Video surface (will be replaced by VLC widget)
        self.video_surface = MDCard(
            md_bg_color=(0, 0, 0, 1),
            size_hint=(1, 1),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        
        # Placeholder for no video
        self.placeholder_label = MDLabel(
            text="No video playing",
            halign="center",
            theme_text_color="Secondary",
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )
        self.video_surface.add_widget(self.placeholder_label)
        self.add_widget(self.video_surface)
        
        # Control overlay
        self.controls_overlay = self.create_controls_overlay()
        self.add_widget(self.controls_overlay)
        
        # Initially hide controls
        self.controls_visible = False
        self.toggle_controls()
    
    def create_controls_overlay(self):
        """Create video player controls overlay"""
        overlay = MDFloatLayout(
            size_hint=(1, 1),
            md_bg_color=(0, 0, 0, 0.3)
        )
        
        # Bottom control bar
        control_bar = MDCard(
            orientation='horizontal',
            size_hint=(1, None),
            height=dp(80),
            pos_hint={'x': 0, 'y': 0},
            md_bg_color=(0, 0, 0, 0.8),
            padding=dp(10),
            spacing=dp(10)
        )
        
        # Play/Pause button
        self.play_pause_btn = MDIconButton(
            icon="play",
            theme_icon_color="Custom",
            icon_color=(1, 1, 1, 1),
            on_release=self.toggle_playback
        )
        control_bar.add_widget(self.play_pause_btn)
        
        # Progress slider
        self.progress_slider = MDSlider(
            min=0,
            max=100,
            value=0,
            size_hint_x=0.6,
            on_touch_down=self.on_slider_touch_down,
            on_touch_up=self.on_slider_touch_up
        )
        control_bar.add_widget(self.progress_slider)
        
        # Time labels
        time_layout = MDBoxLayout(
            orientation='vertical',
            size_hint_x=None,
            width=dp(80)
        )
        
        self.current_time_label = MDLabel(
            text="00:00",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_size="12sp",
            halign="center"
        )
        
        self.duration_label = MDLabel(
            text="00:00",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_size="12sp",
            halign="center"
        )
        
        time_layout.add_widget(self.current_time_label)
        time_layout.add_widget(self.duration_label)
        control_bar.add_widget(time_layout)
        
        # Volume button
        self.volume_btn = MDIconButton(
            icon="volume-high",
            theme_icon_color="Custom",
            icon_color=(1, 1, 1, 1),
            on_release=self.toggle_mute
        )
        control_bar.add_widget(self.volume_btn)
        
        # Fullscreen button
        self.fullscreen_btn = MDIconButton(
            icon="fullscreen",
            theme_icon_color="Custom",
            icon_color=(1, 1, 1, 1),
            on_release=self.toggle_fullscreen
        )
        control_bar.add_widget(self.fullscreen_btn)
        
        overlay.add_widget(control_bar)
        
        # Top control bar
        top_bar = MDCard(
            orientation='horizontal',
            size_hint=(1, None),
            height=dp(60),
            pos_hint={'x': 0, 'top': 1},
            md_bg_color=(0, 0, 0, 0.8),
            padding=dp(10)
        )
        
        # Title label
        self.title_label = MDLabel(
            text="",
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="H6"
        )
        top_bar.add_widget(self.title_label)
        
        # Close button
        close_btn = MDIconButton(
            icon="close",
            theme_icon_color="Custom",
            icon_color=(1, 1, 1, 1),
            pos_hint={'right': 1},
            on_release=self.close_player
        )
        top_bar.add_widget(close_btn)
        
        overlay.add_widget(top_bar)
        
        # Bind touch events for showing/hiding controls
        overlay.bind(on_touch_down=self.on_overlay_touch)
        
        return overlay
    
    def initialize_vlc(self):
        """Initialize VLC media player"""
        if not VLC_AVAILABLE:
            return False
        
        try:
            # Create VLC instance with options
            vlc_args = [
                '--intf', 'dummy',
                '--no-xlib',
                '--quiet',
                '--no-video-title-show'
            ]
            
            # Add hardware acceleration if enabled
            if self.player_settings.hardware_acceleration:
                vlc_args.extend(['--avcodec-hw', 'any'])
            
            self.vlc_instance = vlc.Instance(vlc_args)
            self.vlc_player = self.vlc_instance.media_player_new()
            
            # Set up event callbacks
            event_manager = self.vlc_player.event_manager()
            event_manager.event_attach(vlc.EventType.MediaPlayerEndReached, self.on_media_end)
            event_manager.event_attach(vlc.EventType.MediaPlayerEncounteredError, self.on_media_error)
            event_manager.event_attach(vlc.EventType.MediaPlayerPositionChanged, self.on_position_changed)
            
            # Set volume
            self.vlc_player.audio_set_volume(self.volume)
            
            self.logger.info("VLC player initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize VLC: {e}")
            return False
    
    def play_channel(self, channel: Channel):
        """Play a live TV channel"""
        self.current_channel = channel
        self.current_content = None
        self.title_label.text = channel.name
        self.placeholder_label.text = f"Loading {channel.name}..."
        
        self.play_url(channel.url)
        self.dispatch('on_play', channel)
    
    def play_content(self, content: ContentInfo, resume_position: int = 0):
        """Play VOD or series content"""
        self.current_content = content
        self.current_channel = None
        self.title_label.text = content.title
        self.placeholder_label.text = f"Loading {content.title}..."
        
        self.play_url(content.url)
        
        # Resume from last position if enabled
        if resume_position > 0 and self.player_settings.remember_position:
            Clock.schedule_once(lambda dt: self.seek_to_position(resume_position), 2.0)
        
        self.dispatch('on_play', content)
    
    def play_url(self, url: str):
        """Play media from URL"""
        if not self.vlc_player:
            self.show_error("Video player not initialized")
            return
        
        try:
            # Create media
            self.current_media = self.vlc_instance.media_new(url)
            self.vlc_player.set_media(self.current_media)
            
            # Start playback
            self.vlc_player.play()
            self.is_playing = True
            self.play_pause_btn.icon = "pause"
            
            # Start UI update timer
            if self.update_timer:
                self.update_timer.cancel()
            self.update_timer = Clock.schedule_interval(self.update_ui, 1.0)
            
            self.logger.info(f"Started playing: {url}")
            
        except Exception as e:
            self.logger.error(f"Failed to play URL {url}: {e}")
            self.show_error(f"Playback failed: {str(e)}")
    
    def toggle_playback(self, *args):
        """Toggle play/pause"""
        if not self.vlc_player:
            return
        
        if self.is_playing:
            self.pause()
        else:
            self.play()
    
    def play(self):
        """Resume playback"""
        if self.vlc_player:
            self.vlc_player.play()
            self.is_playing = True
            self.play_pause_btn.icon = "pause"
            self.dispatch('on_play')
    
    def pause(self):
        """Pause playback"""
        if self.vlc_player:
            self.vlc_player.pause()
            self.is_playing = False
            self.play_pause_btn.icon = "play"
            self.dispatch('on_pause')
    
    def stop(self):
        """Stop playback"""
        if self.vlc_player:
            self.vlc_player.stop()
            self.is_playing = False
            self.play_pause_btn.icon = "play"
            
            # Save position for content
            if self.current_content and self.position > 0:
                self.save_playback_position()
            
            # Cancel update timer
            if self.update_timer:
                self.update_timer.cancel()
                self.update_timer = None
            
            self.dispatch('on_stop')
    
    def seek_to_position(self, position_seconds: int):
        """Seek to specific position in seconds"""
        if self.vlc_player and self.duration > 0:
            position_ratio = position_seconds / self.duration
            self.vlc_player.set_position(position_ratio)
    
    def toggle_mute(self, *args):
        """Toggle audio mute"""
        if self.vlc_player:
            is_muted = self.vlc_player.audio_get_mute()
            self.vlc_player.audio_set_mute(not is_muted)
            
            if is_muted:
                self.volume_btn.icon = "volume-high"
            else:
                self.volume_btn.icon = "volume-off"
    
    def set_volume(self, volume: int):
        """Set audio volume (0-100)"""
        self.volume = max(0, min(100, volume))
        if self.vlc_player:
            self.vlc_player.audio_set_volume(self.volume)
    
    def toggle_fullscreen(self, *args):
        """Toggle fullscreen mode"""
        self.is_fullscreen = not self.is_fullscreen
        
        if self.is_fullscreen:
            self.fullscreen_btn.icon = "fullscreen-exit"
            # TODO: Implement fullscreen logic
        else:
            self.fullscreen_btn.icon = "fullscreen"
            # TODO: Implement windowed logic
    
    def toggle_controls(self):
        """Show/hide player controls"""
        self.controls_visible = not self.controls_visible
        
        if self.controls_visible:
            self.controls_overlay.opacity = 1
            # Auto-hide controls after 3 seconds
            Clock.schedule_once(lambda dt: self.hide_controls(), 3.0)
        else:
            self.controls_overlay.opacity = 0
    
    def hide_controls(self):
        """Hide player controls"""
        if self.controls_visible:
            self.controls_visible = False
            self.controls_overlay.opacity = 0
    
    def close_player(self, *args):
        """Close the video player"""
        self.stop()
        # TODO: Navigate back to previous screen
    
    def update_ui(self, dt):
        """Update player UI elements"""
        if not self.vlc_player:
            return False
        
        # Update position and duration
        self.position = self.vlc_player.get_time() / 1000  # Convert to seconds
        self.duration = self.vlc_player.get_length() / 1000  # Convert to seconds
        
        # Update progress slider
        if self.duration > 0:
            progress = (self.position / self.duration) * 100
            self.progress_slider.value = progress
        
        # Update time labels
        self.current_time_label.text = self.format_time(self.position)
        self.duration_label.text = self.format_time(self.duration)
        
        # Save position periodically for content
        if (self.current_content and 
            time.time() - self.last_position_save_time > 30):  # Save every 30 seconds
            self.save_playback_position()
            self.last_position_save_time = time.time()
        
        self.dispatch('on_position_change', self.position, self.duration)
        
        return True  # Continue timer
    
    def save_playback_position(self):
        """Save current playback position"""
        if self.current_content and self.position > 0:
            progress = (self.position / self.duration * 100) if self.duration > 0 else 0
            # TODO: Save to database
            self.logger.debug(f"Saving position {self.position}s ({progress:.1f}%) for {self.current_content.title}")
    
    def format_time(self, seconds: float) -> str:
        """Format time in MM:SS or HH:MM:SS format"""
        if seconds < 0:
            return "00:00"
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def show_error(self, message: str):
        """Show error dialog"""
        dialog = MDDialog(
            title="Playback Error",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
        self.dispatch('on_error', message)
    
    # Event handlers
    def on_overlay_touch(self, instance, touch):
        """Handle touch on overlay to show/hide controls"""
        if self.collide_point(*touch.pos):
            self.toggle_controls()
            return True
        return False
    
    def on_slider_touch_down(self, instance, touch):
        """Handle slider touch down"""
        if self.vlc_player:
            self.vlc_player.pause()
    
    def on_slider_touch_up(self, instance, touch):
        """Handle slider touch up"""
        if self.vlc_player and self.duration > 0:
            # Seek to new position
            new_position = (self.progress_slider.value / 100) * self.duration
            self.seek_to_position(new_position)
            
            if self.is_playing:
                self.vlc_player.play()
    
    def on_media_end(self, event):
        """Handle media end event"""
        self.logger.info("Media playback ended")
        self.stop()
    
    def on_media_error(self, event):
        """Handle media error event"""
        self.logger.error("Media playback error")
        self.show_error("Playback error occurred")
    
    def on_position_changed(self, event):
        """Handle position change event"""
        pass  # Handled in update_ui
    
    # Custom events
    def on_play(self, *args):
        """Called when playback starts"""
        pass
    
    def on_pause(self, *args):
        """Called when playback pauses"""
        pass
    
    def on_stop(self, *args):
        """Called when playback stops"""
        pass
    
    def on_position_change(self, position, duration):
        """Called when playback position changes"""
        pass
    
    def on_error(self, message):
        """Called when an error occurs"""
        pass
