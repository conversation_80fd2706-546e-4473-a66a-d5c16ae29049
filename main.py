#!/usr/bin/env python3
"""
IPTV Player - Cross-platform IPTV application
Main entry point for the application
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Check for required dependencies
try:
    from kivy.app import App
    from kivy.config import Config
    from kivymd.app import MDApp
    from kivymd.theming import ThemableBehavior
    from kivymd.uix.screen import MDScreen
    from kivymd.uix.screenmanager import MDScreenManager

    # Configure Kivy before importing other Kivy modules
    Config.set('graphics', 'width', '1280')
    Config.set('graphics', 'height', '720')
    Config.set('graphics', 'minimum_width', '800')
    Config.set('graphics', 'minimum_height', '600')
    Config.set('graphics', 'resizable', True)

    DEPENDENCIES_AVAILABLE = True

except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Please install required packages:")
    print("pip install -r requirements.txt")
    DEPENDENCIES_AVAILABLE = False

if DEPENDENCIES_AVAILABLE:
    from src.ui.main_screen import MainScreen
    from src.core.app_config import AppConfig
    from src.core.database import DatabaseManager
    from src.core.logger import setup_logger

    class IPTVPlayerApp(MDApp):
        """Main application class for IPTV Player"""

        def __init__(self, **kwargs):
            super().__init__(**kwargs)
            self.title = "IPTV Player"
            self.theme_cls.theme_style = "Dark"
            self.theme_cls.primary_palette = "Blue"
            self.theme_cls.accent_palette = "Amber"

            # Initialize core components
            self.config_manager = AppConfig()
            self.db_manager = DatabaseManager()
            self.logger = setup_logger()

        def build(self):
            """Build the main application interface"""
            self.logger.info("Starting IPTV Player application")

            # Initialize database
            self.db_manager.initialize_database()

            # Create screen manager
            screen_manager = MDScreenManager()

            # Add main screen
            main_screen = MainScreen(name='main')
            screen_manager.add_widget(main_screen)

            return screen_manager

        def on_start(self):
            """Called when the application starts"""
            self.logger.info("Application started successfully")

        def on_stop(self):
            """Called when the application stops"""
            self.logger.info("Application stopping")
            self.db_manager.close()

else:
    # Dummy class when dependencies are not available
    class IPTVPlayerApp:
        def run(self):
            print("Dependencies not available. Cannot run GUI application.")


def main():
    """Main entry point"""
    if not DEPENDENCIES_AVAILABLE:
        print("\nTo test the core functionality without UI dependencies, run:")
        print("python test_app.py")
        sys.exit(1)

    try:
        # Create data directories if they don't exist
        data_dir = Path.home() / '.iptv_player'
        data_dir.mkdir(exist_ok=True)

        (data_dir / 'cache').mkdir(exist_ok=True)
        (data_dir / 'logs').mkdir(exist_ok=True)
        (data_dir / 'playlists').mkdir(exist_ok=True)

        # Run the application
        IPTVPlayerApp().run()

    except Exception as e:
        print(f"Failed to start application: {e}")
        print("\nFor testing without full dependencies, run:")
        print("python test_app.py")
        sys.exit(1)


if __name__ == '__main__':
    main()
