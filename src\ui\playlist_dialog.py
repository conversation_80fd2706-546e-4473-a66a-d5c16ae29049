"""
Playlist dialog for adding/editing playlists
"""

from kivymd.uix.dialog import MD<PERSON>ialog
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.button import MD<PERSON><PERSON>Button

from ..core.logger import LoggerMixin


class PlaylistDialog(MDDialog, LoggerMixin):
    """Dialog for adding/editing playlists"""
    
    def __init__(self, on_save=None, **kwargs):
        self.on_save = on_save
        
        content = MDBoxLayout(
            orientation='vertical',
            spacing="12dp",
            size_hint_y=None,
            height="200dp"
        )
        
        content.add_widget(MDLabel(
            text="Playlist Dialog\n(Coming Soon)",
            halign="center",
            theme_text_color="Secondary"
        ))
        
        super().__init__(
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(
                    text="CANCEL",
                    on_release=self.dismiss
                ),
                MDFlatButton(
                    text="SAVE",
                    on_release=self.save_playlist
                )
            ],
            **kwargs
        )
    
    def save_playlist(self, *args):
        """Save playlist"""
        self.logger.info("Save playlist requested")
        self.dismiss()
