"""
Settings screen for IPTV Player
"""

from typing import Dict, Any, Optional
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, OneLineIconListItem, TwoLineIconListItem
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton, MDFlatButton, MDRaisedButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDSwitch, MDCheckbox
from kivymd.uix.slider import MDSlider
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.expansionpanel import MDExpansionPanel, MDExpansionPanelOneLine
from kivy.metrics import dp

from ..core.logger import LoggerMixin
from ..core.app_config import get_config
from ..core.cache_manager import get_cache_manager
from ..core.security import SecurityManager
from ..models.user_preferences import UserPreferences, PlayerSettings, UISettings, SecuritySettings


class SettingsScreen(MDBoxLayout, LoggerMixin):
    """Comprehensive settings screen"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.config = get_config()
        self.cache_manager = get_cache_manager()
        self.security_manager = SecurityManager()
        self.user_preferences = UserPreferences()  # TODO: Load from database

        self.build_ui()

    def build_ui(self):
        """Build the settings UI"""
        # Top toolbar
        toolbar = MDTopAppBar(
            title="Settings",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[["content-save", lambda x: self.save_settings()]]
        )
        self.add_widget(toolbar)

        # Settings content
        scroll = MDScrollView()
        self.settings_list = MDList()
        scroll.add_widget(self.settings_list)
        self.add_widget(scroll)

        # Build settings sections
        self.build_player_settings()
        self.build_ui_settings()
        self.build_security_settings()
        self.build_cache_settings()
        self.build_about_section()

    def build_player_settings(self):
        """Build player settings section"""
        # Player Settings Header
        player_header = self.create_section_header("Player Settings", "play-circle")
        self.settings_list.add_widget(player_header)

        # Video Backend
        backend_item = TwoLineIconListItem(
            text="Video Backend",
            secondary_text=f"Current: {self.user_preferences.player_settings.backend.value}",
            on_release=self.show_backend_selection
        )
        backend_item.add_widget(MDIconButton(icon="video", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(backend_item)

        # Auto Play
        autoplay_item = OneLineIconListItem(text="Auto Play")
        autoplay_item.add_widget(MDIconButton(icon="play-circle", pos_hint={"center_y": 0.5}))
        autoplay_switch = MDSwitch(
            active=self.user_preferences.player_settings.auto_play,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_autoplay_changed
        )
        autoplay_item.add_widget(autoplay_switch)
        self.settings_list.add_widget(autoplay_item)

        # Remember Position
        remember_item = OneLineIconListItem(text="Remember Playback Position")
        remember_item.add_widget(MDIconButton(icon="bookmark", pos_hint={"center_y": 0.5}))
        remember_switch = MDSwitch(
            active=self.user_preferences.player_settings.remember_position,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_remember_position_changed
        )
        remember_item.add_widget(remember_switch)
        self.settings_list.add_widget(remember_item)

        # Volume
        volume_item = TwoLineIconListItem(
            text="Default Volume",
            secondary_text=f"{int(self.user_preferences.player_settings.volume * 100)}%"
        )
        volume_item.add_widget(MDIconButton(icon="volume-high", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(volume_item)

        # Volume slider
        volume_slider = MDSlider(
            min=0,
            max=1,
            value=self.user_preferences.player_settings.volume,
            on_value=self.on_volume_changed
        )
        volume_card = MDCard(
            padding=dp(16),
            size_hint_y=None,
            height=dp(60)
        )
        volume_card.add_widget(volume_slider)
        self.settings_list.add_widget(volume_card)

        # Hardware Acceleration
        hw_accel_item = OneLineIconListItem(text="Hardware Acceleration")
        hw_accel_item.add_widget(MDIconButton(icon="chip", pos_hint={"center_y": 0.5}))
        hw_accel_switch = MDSwitch(
            active=self.user_preferences.player_settings.hardware_acceleration,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_hw_accel_changed
        )
        hw_accel_item.add_widget(hw_accel_switch)
        self.settings_list.add_widget(hw_accel_item)

        # Buffer Size
        buffer_item = TwoLineIconListItem(
            text="Buffer Size",
            secondary_text=f"{self.user_preferences.player_settings.buffer_size} ms",
            on_release=self.show_buffer_settings
        )
        buffer_item.add_widget(MDIconButton(icon="buffer", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(buffer_item)

    def build_ui_settings(self):
        """Build UI settings section"""
        # UI Settings Header
        ui_header = self.create_section_header("Interface Settings", "palette")
        self.settings_list.add_widget(ui_header)

        # Theme
        theme_item = TwoLineIconListItem(
            text="Theme",
            secondary_text=f"Current: {self.user_preferences.ui_settings.theme.title()}",
            on_release=self.show_theme_selection
        )
        theme_item.add_widget(MDIconButton(icon="palette", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(theme_item)

        # View Mode
        view_mode_item = TwoLineIconListItem(
            text="Default View Mode",
            secondary_text=f"Current: {self.user_preferences.ui_settings.view_mode.value.title()}",
            on_release=self.show_view_mode_selection
        )
        view_mode_item.add_widget(MDIconButton(icon="view-grid", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(view_mode_item)

        # Show Channel Numbers
        channel_numbers_item = OneLineIconListItem(text="Show Channel Numbers")
        channel_numbers_item.add_widget(MDIconButton(icon="numeric", pos_hint={"center_y": 0.5}))
        channel_numbers_switch = MDSwitch(
            active=self.user_preferences.ui_settings.show_channel_numbers,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_channel_numbers_changed
        )
        channel_numbers_item.add_widget(channel_numbers_switch)
        self.settings_list.add_widget(channel_numbers_item)

        # Show Channel Logos
        logos_item = OneLineIconListItem(text="Show Channel Logos")
        logos_item.add_widget(MDIconButton(icon="image", pos_hint={"center_y": 0.5}))
        logos_switch = MDSwitch(
            active=self.user_preferences.ui_settings.show_channel_logos,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_logos_changed
        )
        logos_item.add_widget(logos_switch)
        self.settings_list.add_widget(logos_item)

        # Show EPG Info
        epg_item = OneLineIconListItem(text="Show EPG Information")
        epg_item.add_widget(MDIconButton(icon="television-guide", pos_hint={"center_y": 0.5}))
        epg_switch = MDSwitch(
            active=self.user_preferences.ui_settings.show_epg_info,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_epg_info_changed
        )
        epg_item.add_widget(epg_switch)
        self.settings_list.add_widget(epg_item)

        # Grid Columns
        grid_cols_item = TwoLineIconListItem(
            text="Grid Columns",
            secondary_text=f"{self.user_preferences.ui_settings.grid_columns} columns",
            on_release=self.show_grid_columns_settings
        )
        grid_cols_item.add_widget(MDIconButton(icon="grid", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(grid_cols_item)

    def build_security_settings(self):
        """Build security settings section"""
        # Security Settings Header
        security_header = self.create_section_header("Security & Parental Controls", "shield-check")
        self.settings_list.add_widget(security_header)

        # Parental Control
        parental_item = OneLineIconListItem(text="Enable Parental Controls")
        parental_item.add_widget(MDIconButton(icon="shield-account", pos_hint={"center_y": 0.5}))
        parental_switch = MDSwitch(
            active=self.user_preferences.security_settings.parental_control_enabled,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_parental_control_changed
        )
        parental_item.add_widget(parental_switch)
        self.settings_list.add_widget(parental_item)

        # Set PIN
        pin_item = TwoLineIconListItem(
            text="Parental Control PIN",
            secondary_text="Tap to set or change PIN",
            on_release=self.show_pin_settings
        )
        pin_item.add_widget(MDIconButton(icon="lock", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(pin_item)

        # Content Rating Limit
        rating_item = TwoLineIconListItem(
            text="Content Rating Limit",
            secondary_text=f"Current: {self.user_preferences.security_settings.content_rating_limit or 'None'}",
            on_release=self.show_rating_settings
        )
        rating_item.add_widget(MDIconButton(icon="certificate", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(rating_item)

        # Auto Lock Timeout
        auto_lock_item = TwoLineIconListItem(
            text="Auto Lock Timeout",
            secondary_text=f"{self.user_preferences.security_settings.auto_lock_timeout} minutes" if self.user_preferences.security_settings.auto_lock_timeout > 0 else "Disabled",
            on_release=self.show_auto_lock_settings
        )
        auto_lock_item.add_widget(MDIconButton(icon="timer-lock", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(auto_lock_item)

    def build_cache_settings(self):
        """Build cache and performance settings section"""
        # Cache Settings Header
        cache_header = self.create_section_header("Cache & Performance", "cached")
        self.settings_list.add_widget(cache_header)

        # Enable Cache
        cache_item = OneLineIconListItem(text="Enable Caching")
        cache_item.add_widget(MDIconButton(icon="cached", pos_hint={"center_y": 0.5}))
        cache_switch = MDSwitch(
            active=self.user_preferences.cache_enabled,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_cache_enabled_changed
        )
        cache_item.add_widget(cache_switch)
        self.settings_list.add_widget(cache_item)

        # Cache Size
        cache_size_item = TwoLineIconListItem(
            text="Cache Size Limit",
            secondary_text=f"{self.user_preferences.cache_size_mb} MB",
            on_release=self.show_cache_size_settings
        )
        cache_size_item.add_widget(MDIconButton(icon="harddisk", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(cache_size_item)

        # Cache Statistics
        cache_stats = self.cache_manager.get_cache_statistics()
        stats_item = TwoLineIconListItem(
            text="Cache Usage",
            secondary_text=f"{cache_stats['total_entries']} items, {cache_stats['total_size_mb']:.1f} MB",
            on_release=self.show_cache_statistics
        )
        stats_item.add_widget(MDIconButton(icon="chart-pie", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(stats_item)

        # Clear Cache
        clear_cache_item = OneLineIconListItem(
            text="Clear Cache",
            on_release=self.show_clear_cache_dialog
        )
        clear_cache_item.add_widget(MDIconButton(icon="delete", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(clear_cache_item)

        # Auto Refresh Playlists
        auto_refresh_item = OneLineIconListItem(text="Auto Refresh Playlists")
        auto_refresh_item.add_widget(MDIconButton(icon="refresh", pos_hint={"center_y": 0.5}))
        auto_refresh_switch = MDSwitch(
            active=self.user_preferences.auto_refresh_playlists,
            pos_hint={"center_y": 0.5, "right": 1},
            on_active=self.on_auto_refresh_changed
        )
        auto_refresh_item.add_widget(auto_refresh_switch)
        self.settings_list.add_widget(auto_refresh_item)

    def build_about_section(self):
        """Build about section"""
        # About Header
        about_header = self.create_section_header("About", "information")
        self.settings_list.add_widget(about_header)

        # App Version
        version_item = TwoLineIconListItem(
            text="IPTV Player",
            secondary_text=f"Version {self.config.app_version}"
        )
        version_item.add_widget(MDIconButton(icon="information", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(version_item)

        # Data Directory
        data_dir_item = TwoLineIconListItem(
            text="Data Directory",
            secondary_text=self.config.data_dir,
            on_release=lambda x: self.show_info_dialog("Data Directory", self.config.data_dir)
        )
        data_dir_item.add_widget(MDIconButton(icon="folder", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(data_dir_item)

        # Reset Settings
        reset_item = OneLineIconListItem(
            text="Reset to Defaults",
            on_release=self.show_reset_dialog
        )
        reset_item.add_widget(MDIconButton(icon="restore", pos_hint={"center_y": 0.5}))
        self.settings_list.add_widget(reset_item)

    def create_section_header(self, title: str, icon: str) -> MDCard:
        """Create a section header card"""
        header_card = MDCard(
            md_bg_color=self.theme_cls.primary_color,
            size_hint_y=None,
            height=dp(48),
            padding=dp(16)
        )

        header_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(16)
        )

        header_icon = MDIconButton(
            icon=icon,
            theme_icon_color="Custom",
            icon_color=(1, 1, 1, 1)
        )
        header_layout.add_widget(header_icon)

        header_label = MDLabel(
            text=title,
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            font_style="Subtitle1"
        )
        header_layout.add_widget(header_label)

        header_card.add_widget(header_layout)
        return header_card

    # Event handlers
    def go_back(self):
        """Go back to previous screen"""
        self.logger.info("Going back from settings")
        # TODO: Implement navigation back

    def save_settings(self):
        """Save current settings"""
        try:
            # TODO: Save to database
            self.logger.info("Settings saved")
            self.show_info_dialog("Settings", "Settings saved successfully!")
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")
            self.show_error_dialog("Error", "Failed to save settings")

    # Player settings handlers
    def show_backend_selection(self, *args):
        """Show video backend selection dialog"""
        from ..models.user_preferences import PlayerBackend

        menu_items = [
            {"text": "VLC", "on_release": lambda: self.set_backend(PlayerBackend.VLC)},
            {"text": "System Default", "on_release": lambda: self.set_backend(PlayerBackend.SYSTEM_DEFAULT)},
        ]

        self.backend_menu = MDDropdownMenu(
            caller=args[0] if args else None,
            items=menu_items,
            width_mult=4
        )
        self.backend_menu.open()

    def set_backend(self, backend):
        """Set video backend"""
        self.user_preferences.player_settings.backend = backend
        self.backend_menu.dismiss()
        self.logger.info(f"Video backend set to: {backend.value}")
        # Refresh the UI
        self.settings_list.clear_widgets()
        self.build_player_settings()
        self.build_ui_settings()
        self.build_security_settings()
        self.build_cache_settings()
        self.build_about_section()

    def on_autoplay_changed(self, instance, value):
        """Handle autoplay setting change"""
        self.user_preferences.player_settings.auto_play = value
        self.logger.info(f"Auto play set to: {value}")

    def on_remember_position_changed(self, instance, value):
        """Handle remember position setting change"""
        self.user_preferences.player_settings.remember_position = value
        self.logger.info(f"Remember position set to: {value}")

    def on_volume_changed(self, instance, value):
        """Handle volume change"""
        self.user_preferences.player_settings.volume = value
        self.logger.debug(f"Volume set to: {int(value * 100)}%")

    def on_hw_accel_changed(self, instance, value):
        """Handle hardware acceleration setting change"""
        self.user_preferences.player_settings.hardware_acceleration = value
        self.logger.info(f"Hardware acceleration set to: {value}")

    def show_buffer_settings(self, *args):
        """Show buffer size settings dialog"""
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint_y=None,
            height=dp(120)
        )

        buffer_field = MDTextField(
            hint_text="Buffer size (milliseconds)",
            text=str(self.user_preferences.player_settings.buffer_size),
            input_filter="int"
        )
        content.add_widget(buffer_field)

        dialog = MDDialog(
            title="Buffer Size Settings",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="CANCEL", on_release=lambda x: dialog.dismiss()),
                MDFlatButton(
                    text="SAVE",
                    on_release=lambda x: self.set_buffer_size(buffer_field.text, dialog)
                )
            ]
        )
        dialog.open()

    def set_buffer_size(self, value: str, dialog: MDDialog):
        """Set buffer size"""
        try:
            buffer_size = int(value)
            if 1000 <= buffer_size <= 30000:  # 1-30 seconds
                self.user_preferences.player_settings.buffer_size = buffer_size
                self.logger.info(f"Buffer size set to: {buffer_size} ms")
                dialog.dismiss()
            else:
                self.show_error_dialog("Invalid Value", "Buffer size must be between 1000 and 30000 ms")
        except ValueError:
            self.show_error_dialog("Invalid Value", "Please enter a valid number")

    # UI settings handlers
    def show_theme_selection(self, *args):
        """Show theme selection dialog"""
        menu_items = [
            {"text": "Dark", "on_release": lambda: self.set_theme("dark")},
            {"text": "Light", "on_release": lambda: self.set_theme("light")},
            {"text": "Auto", "on_release": lambda: self.set_theme("auto")},
        ]

        self.theme_menu = MDDropdownMenu(
            caller=args[0] if args else None,
            items=menu_items,
            width_mult=4
        )
        self.theme_menu.open()

    def set_theme(self, theme: str):
        """Set app theme"""
        self.user_preferences.ui_settings.theme = theme
        self.theme_menu.dismiss()
        self.logger.info(f"Theme set to: {theme}")
        # TODO: Apply theme change immediately

    def show_view_mode_selection(self, *args):
        """Show view mode selection dialog"""
        from ..models.user_preferences import ViewMode

        menu_items = [
            {"text": "List", "on_release": lambda: self.set_view_mode(ViewMode.LIST)},
            {"text": "Grid", "on_release": lambda: self.set_view_mode(ViewMode.GRID)},
            {"text": "Compact", "on_release": lambda: self.set_view_mode(ViewMode.COMPACT)},
        ]

        self.view_mode_menu = MDDropdownMenu(
            caller=args[0] if args else None,
            items=menu_items,
            width_mult=4
        )
        self.view_mode_menu.open()

    def set_view_mode(self, view_mode):
        """Set default view mode"""
        self.user_preferences.ui_settings.view_mode = view_mode
        self.view_mode_menu.dismiss()
        self.logger.info(f"View mode set to: {view_mode.value}")

    def on_channel_numbers_changed(self, instance, value):
        """Handle show channel numbers setting change"""
        self.user_preferences.ui_settings.show_channel_numbers = value
        self.logger.info(f"Show channel numbers set to: {value}")

    def on_logos_changed(self, instance, value):
        """Handle show logos setting change"""
        self.user_preferences.ui_settings.show_channel_logos = value
        self.logger.info(f"Show channel logos set to: {value}")

    def on_epg_info_changed(self, instance, value):
        """Handle show EPG info setting change"""
        self.user_preferences.ui_settings.show_epg_info = value
        self.logger.info(f"Show EPG info set to: {value}")

    def show_grid_columns_settings(self, *args):
        """Show grid columns settings dialog"""
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint_y=None,
            height=dp(120)
        )

        columns_field = MDTextField(
            hint_text="Number of columns (2-6)",
            text=str(self.user_preferences.ui_settings.grid_columns),
            input_filter="int"
        )
        content.add_widget(columns_field)

        dialog = MDDialog(
            title="Grid Columns Settings",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="CANCEL", on_release=lambda x: dialog.dismiss()),
                MDFlatButton(
                    text="SAVE",
                    on_release=lambda x: self.set_grid_columns(columns_field.text, dialog)
                )
            ]
        )
        dialog.open()

    def set_grid_columns(self, value: str, dialog: MDDialog):
        """Set grid columns"""
        try:
            columns = int(value)
            if 2 <= columns <= 6:
                self.user_preferences.ui_settings.grid_columns = columns
                self.logger.info(f"Grid columns set to: {columns}")
                dialog.dismiss()
            else:
                self.show_error_dialog("Invalid Value", "Number of columns must be between 2 and 6")
        except ValueError:
            self.show_error_dialog("Invalid Value", "Please enter a valid number")

    # Security settings handlers
    def on_parental_control_changed(self, instance, value):
        """Handle parental control setting change"""
        self.user_preferences.security_settings.parental_control_enabled = value
        self.logger.info(f"Parental control set to: {value}")

    def show_pin_settings(self, *args):
        """Show PIN settings dialog"""
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint_y=None,
            height=dp(200)
        )

        if self.user_preferences.security_settings.parental_pin:
            current_pin_field = MDTextField(
                hint_text="Current PIN",
                password=True,
                max_text_length=6
            )
            content.add_widget(current_pin_field)

        new_pin_field = MDTextField(
            hint_text="New PIN (4-6 digits)",
            password=True,
            max_text_length=6,
            input_filter="int"
        )
        content.add_widget(new_pin_field)

        confirm_pin_field = MDTextField(
            hint_text="Confirm PIN",
            password=True,
            max_text_length=6,
            input_filter="int"
        )
        content.add_widget(confirm_pin_field)

        dialog = MDDialog(
            title="Set Parental Control PIN",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="CANCEL", on_release=lambda x: dialog.dismiss()),
                MDFlatButton(
                    text="SAVE",
                    on_release=lambda x: self.set_pin(
                        current_pin_field.text if self.user_preferences.security_settings.parental_pin else "",
                        new_pin_field.text,
                        confirm_pin_field.text,
                        dialog
                    )
                )
            ]
        )
        dialog.open()

    def set_pin(self, current_pin: str, new_pin: str, confirm_pin: str, dialog: MDDialog):
        """Set parental control PIN"""
        # Validate current PIN if one exists
        if self.user_preferences.security_settings.parental_pin:
            if not self.security_manager.verify_pin(current_pin, self.user_preferences.security_settings.parental_pin):
                self.show_error_dialog("Invalid PIN", "Current PIN is incorrect")
                return

        # Validate new PIN
        if len(new_pin) < 4:
            self.show_error_dialog("Invalid PIN", "PIN must be at least 4 digits")
            return

        if new_pin != confirm_pin:
            self.show_error_dialog("PIN Mismatch", "New PIN and confirmation don't match")
            return

        # Set new PIN
        hashed_pin = self.security_manager.hash_pin(new_pin)
        self.user_preferences.security_settings.parental_pin = hashed_pin
        self.logger.info("Parental control PIN updated")
        dialog.dismiss()
        self.show_info_dialog("PIN Updated", "Parental control PIN has been updated successfully")

    # Cache settings handlers
    def on_cache_enabled_changed(self, instance, value):
        """Handle cache enabled setting change"""
        self.user_preferences.cache_enabled = value
        self.logger.info(f"Cache enabled set to: {value}")

    def on_auto_refresh_changed(self, instance, value):
        """Handle auto refresh setting change"""
        self.user_preferences.auto_refresh_playlists = value
        self.logger.info(f"Auto refresh playlists set to: {value}")

    def show_cache_size_settings(self, *args):
        """Show cache size settings dialog"""
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint_y=None,
            height=dp(120)
        )

        size_field = MDTextField(
            hint_text="Cache size limit (MB)",
            text=str(self.user_preferences.cache_size_mb),
            input_filter="int"
        )
        content.add_widget(size_field)

        dialog = MDDialog(
            title="Cache Size Settings",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="CANCEL", on_release=lambda x: dialog.dismiss()),
                MDFlatButton(
                    text="SAVE",
                    on_release=lambda x: self.set_cache_size(size_field.text, dialog)
                )
            ]
        )
        dialog.open()

    def set_cache_size(self, value: str, dialog: MDDialog):
        """Set cache size limit"""
        try:
            size_mb = int(value)
            if 50 <= size_mb <= 5000:  # 50MB to 5GB
                self.user_preferences.cache_size_mb = size_mb
                self.logger.info(f"Cache size limit set to: {size_mb} MB")
                dialog.dismiss()
                # Apply new limit
                self.cache_manager.cleanup_cache_by_size(size_mb)
            else:
                self.show_error_dialog("Invalid Value", "Cache size must be between 50 and 5000 MB")
        except ValueError:
            self.show_error_dialog("Invalid Value", "Please enter a valid number")

    def show_cache_statistics(self, *args):
        """Show cache statistics dialog"""
        stats = self.cache_manager.get_cache_statistics()

        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(12),
            size_hint_y=None,
            adaptive_height=True
        )

        # Total usage
        total_label = MDLabel(
            text=f"Total: {stats['total_entries']} items, {stats['total_size_mb']:.1f} MB",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        content.add_widget(total_label)

        # Breakdown by type
        for cache_type, type_stats in stats['type_breakdown'].items():
            type_label = MDLabel(
                text=f"{cache_type.title()}: {type_stats['count']} items, {type_stats['size_bytes'] / 1024 / 1024:.1f} MB",
                theme_text_color="Secondary",
                font_style="Body2"
            )
            content.add_widget(type_label)

        # Memory cache
        memory_label = MDLabel(
            text=f"Memory Cache: {stats['memory_cache_entries']} items",
            theme_text_color="Secondary",
            font_style="Body2"
        )
        content.add_widget(memory_label)

        dialog = MDDialog(
            title="Cache Statistics",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="CLOSE", on_release=lambda x: dialog.dismiss())
            ]
        )
        dialog.open()

    def show_clear_cache_dialog(self, *args):
        """Show clear cache confirmation dialog"""
        dialog = MDDialog(
            title="Clear Cache",
            text="Are you sure you want to clear all cached data? This will free up storage space but may slow down the app temporarily.",
            buttons=[
                MDFlatButton(text="CANCEL", on_release=lambda x: dialog.dismiss()),
                MDFlatButton(
                    text="CLEAR",
                    on_release=lambda x: self.clear_cache(dialog)
                )
            ]
        )
        dialog.open()

    def clear_cache(self, dialog: MDDialog):
        """Clear all cache data"""
        try:
            self.cache_manager.clear_all_cache()
            dialog.dismiss()
            self.show_info_dialog("Cache Cleared", "All cached data has been cleared successfully")
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
            self.show_error_dialog("Error", "Failed to clear cache")

    # Utility methods
    def show_info_dialog(self, title: str, message: str):
        """Show information dialog"""
        dialog = MDDialog(
            title=title,
            text=message,
            buttons=[
                MDFlatButton(text="OK", on_release=lambda x: dialog.dismiss())
            ]
        )
        dialog.open()

    def show_error_dialog(self, title: str, message: str):
        """Show error dialog"""
        dialog = MDDialog(
            title=title,
            text=message,
            buttons=[
                MDFlatButton(text="OK", on_release=lambda x: dialog.dismiss())
            ]
        )
        dialog.open()

    def show_reset_dialog(self, *args):
        """Show reset settings confirmation dialog"""
        dialog = MDDialog(
            title="Reset Settings",
            text="Are you sure you want to reset all settings to their default values? This action cannot be undone.",
            buttons=[
                MDFlatButton(text="CANCEL", on_release=lambda x: dialog.dismiss()),
                MDFlatButton(
                    text="RESET",
                    on_release=lambda x: self.reset_settings(dialog)
                )
            ]
        )
        dialog.open()

    def reset_settings(self, dialog: MDDialog):
        """Reset all settings to defaults"""
        try:
            self.user_preferences = UserPreferences()
            dialog.dismiss()
            self.show_info_dialog("Settings Reset", "All settings have been reset to their default values")
            # Rebuild UI with default values
            self.settings_list.clear_widgets()
            self.build_player_settings()
            self.build_ui_settings()
            self.build_security_settings()
            self.build_cache_settings()
            self.build_about_section()
        except Exception as e:
            self.logger.error(f"Failed to reset settings: {e}")
            self.show_error_dialog("Error", "Failed to reset settings")
