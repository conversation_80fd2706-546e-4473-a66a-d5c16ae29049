"""
Main screen for IPTV Player application
"""

from kivy.clock import Clock
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.navigationdrawer import <PERSON><PERSON>avi<PERSON>Drawer, MDNavigationDrawerMenu
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.bottomnavigation import MDBottomNavigation, MDBottomNavigationItem
from kivymd.uix.list import MDList, OneLineIconListItem
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton, MDFabButton
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.icon_definitions import md_icons

from ..core.logger import LoggerMixin
from .channel_list import ChannelListView
from .vod_browser import VODBrowserView
from .series_browser import SeriesBrowserView
from .settings_screen import SettingsScreen


class MainScreen(MDScreen, LoggerMixin):
    """Main application screen with navigation"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.current_playlist = None
        self.playlists = []
        self.build_ui()
    
    def build_ui(self):
        """Build the main UI layout"""
        # Main layout
        main_layout = MDBoxLayout(orientation='vertical')
        
        # Top app bar
        self.toolbar = MDTopAppBar(
            title="IPTV Player",
            left_action_items=[["menu", lambda x: self.toggle_nav_drawer()]],
            right_action_items=[
                ["playlist-plus", lambda x: self.show_add_playlist_dialog()],
                ["cog", lambda x: self.show_settings()]
            ]
        )
        main_layout.add_widget(self.toolbar)
        
        # Navigation drawer
        self.nav_drawer = self.create_navigation_drawer()
        
        # Bottom navigation
        self.bottom_nav = self.create_bottom_navigation()
        main_layout.add_widget(self.bottom_nav)
        
        # Add navigation drawer to main layout
        drawer_layout = MDBoxLayout()
        drawer_layout.add_widget(main_layout)
        drawer_layout.add_widget(self.nav_drawer)
        
        self.add_widget(drawer_layout)
        
        # Load initial data
        Clock.schedule_once(self.load_initial_data, 0.5)
    
    def create_navigation_drawer(self):
        """Create navigation drawer with playlists and options"""
        drawer = MDNavigationDrawer(
            type="standard",
            close_on_click=True
        )
        
        drawer_menu = MDNavigationDrawerMenu()
        
        # Playlists section
        playlists_header = MDLabel(
            text="Playlists",
            theme_text_color="Primary",
            size_hint_y=None,
            height="48dp"
        )
        drawer_menu.add_widget(playlists_header)
        
        # Playlist list (will be populated dynamically)
        self.playlist_list = MDList()
        drawer_menu.add_widget(self.playlist_list)
        
        # Add playlist button
        add_playlist_item = OneLineIconListItem(
            text="Add Playlist",
            on_release=self.show_add_playlist_dialog
        )
        add_playlist_item.add_widget(MDIconButton(
            icon="playlist-plus",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color
        ))
        drawer_menu.add_widget(add_playlist_item)
        
        # Favorites
        favorites_item = OneLineIconListItem(
            text="Favorites",
            on_release=self.show_favorites
        )
        favorites_item.add_widget(MDIconButton(
            icon="heart",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.accent_color
        ))
        drawer_menu.add_widget(favorites_item)
        
        # Recently watched
        recent_item = OneLineIconListItem(
            text="Recently Watched",
            on_release=self.show_recent
        )
        recent_item.add_widget(MDIconButton(
            icon="history",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color
        ))
        drawer_menu.add_widget(recent_item)
        
        drawer.add_widget(drawer_menu)
        return drawer
    
    def create_bottom_navigation(self):
        """Create bottom navigation with main sections"""
        bottom_nav = MDBottomNavigation(
            panel_color=self.theme_cls.primary_color,
            selected_color_background=self.theme_cls.primary_light,
            text_color_active=self.theme_cls.primary_color
        )
        
        # Live TV tab
        live_tv_item = MDBottomNavigationItem(
            name='live_tv',
            text='Live TV',
            icon='television'
        )
        self.channel_list_view = ChannelListView()
        live_tv_item.add_widget(self.channel_list_view)
        bottom_nav.add_widget(live_tv_item)
        
        # VOD tab
        vod_item = MDBottomNavigationItem(
            name='vod',
            text='Movies',
            icon='movie'
        )
        self.vod_browser_view = VODBrowserView()
        vod_item.add_widget(self.vod_browser_view)
        bottom_nav.add_widget(vod_item)
        
        # Series tab
        series_item = MDBottomNavigationItem(
            name='series',
            text='Series',
            icon='television-classic'
        )
        self.series_browser_view = SeriesBrowserView()
        series_item.add_widget(self.series_browser_view)
        bottom_nav.add_widget(series_item)
        
        # Search tab
        search_item = MDBottomNavigationItem(
            name='search',
            text='Search',
            icon='magnify'
        )
        search_layout = MDBoxLayout(
            orientation='vertical',
            padding="20dp",
            spacing="20dp"
        )
        
        search_field = MDTextField(
            hint_text="Search channels, movies, series...",
            helper_text="Enter search terms",
            helper_text_mode="on_focus",
            icon_right="magnify",
            size_hint_y=None,
            height="56dp"
        )
        search_field.bind(text=self.on_search_text_change)
        
        search_results = MDList()
        
        search_layout.add_widget(search_field)
        search_layout.add_widget(search_results)
        search_item.add_widget(search_layout)
        bottom_nav.add_widget(search_item)
        
        return bottom_nav
    
    def toggle_nav_drawer(self):
        """Toggle navigation drawer"""
        self.nav_drawer.set_state("open" if self.nav_drawer.state == "close" else "close")
    
    def show_add_playlist_dialog(self, *args):
        """Show dialog to add new playlist"""
        from .playlist_dialog import PlaylistDialog
        
        dialog = PlaylistDialog(
            title="Add Playlist",
            on_save=self.on_playlist_added
        )
        dialog.open()
    
    def show_settings(self, *args):
        """Show settings screen"""
        settings_screen = SettingsScreen()
        # In a real app, you would navigate to the settings screen
        # For now, we'll show it as a dialog
        self.logger.info("Settings requested")
    
    def show_favorites(self, *args):
        """Show favorites list"""
        self.bottom_nav.switch_tab('live_tv')
        self.channel_list_view.show_favorites()
        self.nav_drawer.set_state("close")
    
    def show_recent(self, *args):
        """Show recently watched content"""
        self.bottom_nav.switch_tab('live_tv')
        self.channel_list_view.show_recent()
        self.nav_drawer.set_state("close")
    
    def on_search_text_change(self, instance, text):
        """Handle search text changes"""
        if len(text) >= 3:
            self.perform_search(text)
    
    def perform_search(self, query):
        """Perform search across all content"""
        self.logger.info(f"Searching for: {query}")
        # TODO: Implement search functionality
    
    def load_initial_data(self, dt):
        """Load initial data on startup"""
        self.logger.info("Loading initial data...")
        self.load_playlists()
    
    def load_playlists(self):
        """Load playlists from database"""
        try:
            # TODO: Load from database
            self.playlists = []
            self.update_playlist_drawer()
            
            if self.playlists:
                self.select_playlist(self.playlists[0])
            else:
                self.show_welcome_message()
                
        except Exception as e:
            self.logger.error(f"Failed to load playlists: {e}")
    
    def update_playlist_drawer(self):
        """Update playlist list in navigation drawer"""
        self.playlist_list.clear_widgets()
        
        for playlist in self.playlists:
            item = OneLineIconListItem(
                text=playlist.name,
                on_release=lambda x, p=playlist: self.select_playlist(p)
            )
            
            # Add icon based on playlist type
            icon_name = {
                'M3U': 'playlist-music',
                'M3U8': 'playlist-music',
                'XTREAM': 'server',
                'STALKER': 'set-top-box'
            }.get(playlist.playlist_type.value.upper(), 'playlist-music')
            
            item.add_widget(MDIconButton(
                icon=icon_name,
                theme_icon_color="Custom",
                icon_color=self.theme_cls.primary_color
            ))
            
            self.playlist_list.add_widget(item)
    
    def select_playlist(self, playlist):
        """Select and load a playlist"""
        self.current_playlist = playlist
        self.toolbar.title = f"IPTV Player - {playlist.name}"
        
        # Update all views with new playlist
        self.channel_list_view.load_playlist(playlist)
        self.vod_browser_view.load_playlist(playlist)
        self.series_browser_view.load_playlist(playlist)
        
        self.nav_drawer.set_state("close")
        self.logger.info(f"Selected playlist: {playlist.name}")
    
    def on_playlist_added(self, playlist):
        """Handle new playlist addition"""
        self.playlists.append(playlist)
        self.update_playlist_drawer()
        self.select_playlist(playlist)
        self.logger.info(f"Added new playlist: {playlist.name}")
    
    def show_welcome_message(self):
        """Show welcome message when no playlists are available"""
        welcome_card = MDCard(
            orientation='vertical',
            padding="20dp",
            spacing="20dp",
            size_hint=(0.8, 0.6),
            pos_hint={'center_x': 0.5, 'center_y': 0.5},
            elevation=3
        )
        
        welcome_label = MDLabel(
            text="Welcome to IPTV Player!",
            theme_text_color="Primary",
            halign="center",
            font_style="H5"
        )
        
        instruction_label = MDLabel(
            text="Get started by adding your first playlist.\nSupported formats: M3U, M3U8, Xtream Codes, Stalker Portal",
            theme_text_color="Secondary",
            halign="center",
            font_style="Body1"
        )
        
        add_button = MDFabButton(
            icon="plus",
            pos_hint={'center_x': 0.5},
            on_release=self.show_add_playlist_dialog
        )
        
        welcome_card.add_widget(welcome_label)
        welcome_card.add_widget(instruction_label)
        welcome_card.add_widget(add_button)
        
        # Add to current tab
        current_tab = self.bottom_nav.get_current_tab()
        if current_tab:
            current_tab.add_widget(welcome_card)
