# IPTV Player - Comprehensive Documentation

## Overview

IPTV Player is a cross-platform application built with Python, Kivy, and KivyMD that provides a modern interface for watching IPTV content. It supports multiple playlist formats and content providers with advanced features like EPG, favorites, parental controls, and more.

## Features

### Supported Playlist Formats
- **M3U/M3U8**: Standard playlist format with EXTINF metadata support
- **Xtream Codes API**: Username/password authentication with live TV, VOD, and series
- **Stalker Portal**: MAC address authentication (MAG/STB Emulator compatible)

### Content Types
- **Live TV**: Real-time television channels with EPG support
- **VOD (Video on Demand)**: Movies and standalone video content
- **Series**: TV shows with season and episode organization

### User Interface Features
- Modern Material Design interface using KivyMD
- Responsive design for different screen sizes
- Bottom navigation with Live TV, Movies, Series, and Search tabs
- Navigation drawer with playlist management
- Multiple view modes: List, Grid, and Compact
- Category-based organization with filtering
- Search functionality across all content types
- Favorites management
- Recently watched tracking

### Player Features
- VLC backend integration for reliable playback
- Remember playback position for VOD and series
- Subtitle support
- Multiple aspect ratio options
- Hardware acceleration support
- Customizable buffer settings

### Security & Parental Controls
- PIN-based channel locking
- Content rating filters
- Category hiding/showing
- Secure credential storage
- Auto-lock timeout

### Performance Features
- Local SQLite database for fast access
- Intelligent caching system
- Background playlist refresh
- Optimized image loading
- Efficient memory management

## Architecture

### Project Structure
```
iptv-player/
├── main.py                 # Application entry point
├── test_app.py            # Test suite for core functionality
├── requirements.txt       # Python dependencies
├── README.md             # Basic project information
├── DOCUMENTATION.md      # This comprehensive documentation
├── src/
│   ├── core/             # Core application logic
│   │   ├── app_config.py # Application configuration
│   │   ├── database.py   # SQLite database manager
│   │   └── logger.py     # Logging configuration
│   ├── models/           # Data models
│   │   ├── channel.py    # Channel and group models
│   │   ├── playlist.py   # Playlist and credentials models
│   │   ├── content.py    # VOD, series, and episode models
│   │   ├── epg.py        # Electronic Program Guide models
│   │   └── user_preferences.py # User settings and preferences
│   ├── parsers/          # Playlist parsers
│   │   └── m3u_parser.py # M3U/M3U8 format parser
│   ├── providers/        # Content providers
│   │   ├── xtream_provider.py # Xtream Codes API client
│   │   └── stalker_provider.py # Stalker Portal client
│   ├── ui/               # User interface components
│   │   ├── main_screen.py      # Main application screen
│   │   ├── channel_list.py     # Channel list view
│   │   ├── vod_browser.py      # VOD browser view
│   │   ├── series_browser.py   # Series browser view
│   │   ├── settings_screen.py  # Settings interface
│   │   └── playlist_dialog.py  # Playlist management dialog
│   └── utils/            # Utility functions
└── tests/                # Unit tests
```

### Core Components

#### Database Layer (`src/core/database.py`)
- SQLite-based storage for all application data
- Tables for playlists, channels, content, EPG, user preferences, and cache
- Automatic schema migration and data integrity
- Connection pooling and transaction management
- Efficient indexing for fast queries

#### Configuration Management (`src/core/app_config.py`)
- Centralized application configuration
- User-specific settings storage
- Default value management
- Configuration validation and migration
- Environment-specific overrides

#### Logging System (`src/core/logger.py`)
- Multi-level logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- File rotation and size management
- Console and file output
- Performance monitoring decorators
- Exception tracking

### Data Models

#### Channel Model (`src/models/channel.py`)
```python
@dataclass
class Channel:
    id: str
    name: str
    url: str
    group: Optional[str] = None
    logo: Optional[str] = None
    epg_id: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    category: Optional[str] = None
    is_favorite: bool = False
    is_hidden: bool = False
    is_locked: bool = False
    # ... additional fields
```

#### Playlist Model (`src/models/playlist.py`)
```python
@dataclass
class Playlist:
    id: str
    name: str
    playlist_type: PlaylistType  # M3U, M3U8, XTREAM, STALKER
    url: Optional[str] = None
    credentials: Optional[PlaylistCredentials] = None
    is_active: bool = True
    auto_refresh: bool = False
    # ... additional fields
```

#### Content Models (`src/models/content.py`)
- `ContentInfo`: Base class for all content types
- `Series`: TV series with seasons and episodes
- `Season`: Season container with episode list
- `Episode`: Individual episode with metadata

#### EPG Models (`src/models/epg.py`)
- `EPGProgram`: Individual program/show information
- `EPGChannel`: Channel-specific EPG data
- `EPGData`: Container for all EPG information

### Playlist Parsers

#### M3U Parser (`src/parsers/m3u_parser.py`)
- Supports both M3U and M3U8 formats
- Extracts EXTINF metadata (tvg-id, tvg-logo, group-title, etc.)
- Handles multiple character encodings
- URL resolution for relative paths
- Validation and error handling

### Content Providers

#### Xtream Codes Provider (`src/providers/xtream_provider.py`)
- Full API implementation for Xtream Codes servers
- Authentication and session management
- Live TV, VOD, and series content retrieval
- Category and genre support
- Stream URL generation
- Response caching for performance

#### Stalker Portal Provider (`src/providers/stalker_provider.py`)
- MAG/STB device emulation
- MAC address authentication
- Portal API communication
- Live TV and VOD support
- Session management and token handling
- Device information simulation

## Installation and Setup

### Prerequisites
- Python 3.8 or higher
- pip package manager
- Operating System: Windows, macOS, or Linux

### Installation Steps

1. **Clone the repository:**
```bash
git clone <repository-url>
cd iptv-player
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Run the application:**
```bash
python main.py
```

### Testing Without Full Dependencies

If you want to test the core functionality without installing all GUI dependencies:

```bash
python test_app.py
```

This will run tests for:
- Data models
- Database operations
- Playlist parsers
- Content providers
- Configuration system

## Configuration

### Application Configuration

The application stores configuration in `~/.iptv_player/config.json`:

```json
{
  "app_name": "IPTV Player",
  "app_version": "1.0.0",
  "cache_enabled": true,
  "cache_size_mb": 500,
  "default_player_backend": "vlc",
  "log_level": "INFO",
  "window_width": 1280,
  "window_height": 720
}
```

### User Preferences

User preferences are stored in the SQLite database and include:
- Player settings (volume, subtitles, aspect ratio)
- UI settings (theme, view mode, sort order)
- Security settings (parental controls, PIN)
- Favorite channels and recently watched content

### Data Storage Locations

- **Windows**: `%USERPROFILE%\.iptv_player\`
- **macOS/Linux**: `~/.iptv_player/`

Directory structure:
```
.iptv_player/
├── config.json          # Application configuration
├── iptv_player.db       # SQLite database
├── cache/               # Cached images and data
├── logs/                # Application logs
└── playlists/           # Local playlist files
```

## Usage Guide

### Adding Playlists

#### M3U/M3U8 Playlists
1. Click the "+" button in the navigation drawer
2. Select "M3U/M3U8" as the playlist type
3. Enter a name for the playlist
4. Provide either:
   - URL to the playlist file
   - Local file path
5. Click "Save"

#### Xtream Codes
1. Click the "+" button in the navigation drawer
2. Select "Xtream Codes" as the playlist type
3. Enter:
   - Playlist name
   - Server URL
   - Username
   - Password
4. Click "Save"

#### Stalker Portal
1. Click the "+" button in the navigation drawer
2. Select "Stalker Portal" as the playlist type
3. Enter:
   - Playlist name
   - Portal URL
   - MAC Address (format: 00:1A:79:XX:XX:XX)
4. Click "Save"

### Navigating Content

#### Live TV
- Browse channels by category using the category chips
- Switch between List, Grid, and Compact view modes
- Sort channels by name, category, or favorites
- Add channels to favorites by clicking the heart icon
- Search for specific channels using the search tab

#### VOD (Movies)
- Browse movies by genre/category
- View movie details including poster, description, and metadata
- Mark movies as favorites
- Resume playback from last position

#### Series
- Browse series by category
- Navigate seasons and episodes
- Track watch progress
- Resume episode playback

### Player Controls

The integrated video player supports:
- Play/pause, seek, volume control
- Fullscreen mode
- Subtitle selection (if available)
- Aspect ratio adjustment
- Playback speed control

### Settings and Preferences

Access settings through the gear icon in the top toolbar:

#### Player Settings
- Video backend selection (VLC recommended)
- Buffer size and network timeout
- Subtitle preferences
- Hardware acceleration

#### UI Settings
- Theme selection (Dark/Light)
- View mode preferences
- Grid column count
- Font size

#### Security Settings
- Parental control PIN
- Content rating limits
- Auto-lock timeout
- Category restrictions

## API Reference

### Database Operations

```python
from src.core.database import DatabaseManager

# Initialize database
db = DatabaseManager()
db.initialize_database()

# Playlist operations
db.save_playlist(playlist)
playlist = db.get_playlist(playlist_id)
playlists = db.get_all_playlists()

# Channel operations
db.save_channel(channel, playlist_id)
channels = db.get_channels_by_playlist(playlist_id)
db.update_channel_favorite(channel_id, is_favorite)

# Content operations
db.save_content(content, playlist_id)
content_list = db.get_content_by_playlist(playlist_id, content_type)
db.update_content_progress(content_id, progress, position)
```

### Playlist Parsing

```python
from src.parsers.m3u_parser import M3UParser

# Parse M3U content
parser = M3UParser()
channels, groups = parser.parse_content(m3u_content)

# Parse from URL
channels, groups = parser.parse_url("http://example.com/playlist.m3u")

# Validate playlist
is_valid = parser.validate_playlist(content)
```

### Content Providers

```python
from src.providers.xtream_provider import XtreamCodesProvider
from src.models.playlist import PlaylistCredentials, AuthType

# Xtream Codes
credentials = PlaylistCredentials(
    auth_type=AuthType.USERNAME_PASSWORD,
    username="user",
    password="pass",
    server_url="http://server.com"
)

async with XtreamCodesProvider(credentials) as provider:
    if await provider.authenticate():
        channels = await provider.get_live_streams()
        vod_content = await provider.get_vod_streams()
        series = await provider.get_series()
```

## Troubleshooting

### Common Issues

#### Application Won't Start
- Ensure all dependencies are installed: `pip install -r requirements.txt`
- Check Python version: Python 3.8+ required
- Run test suite: `python test_app.py`

#### Playlist Loading Fails
- Verify URL accessibility
- Check authentication credentials
- Review application logs in `~/.iptv_player/logs/`

#### Video Playback Issues
- Ensure VLC is installed on the system
- Check network connectivity
- Verify stream URL validity
- Try different player backend in settings

#### Performance Issues
- Reduce cache size in settings
- Disable hardware acceleration if causing problems
- Clear application cache
- Check available system memory

### Log Files

Application logs are stored in:
- Main log: `~/.iptv_player/logs/iptv_player.log`
- Error log: `~/.iptv_player/logs/errors.log`

Log levels can be adjusted in the configuration file.

### Database Issues

If database corruption occurs:
1. Close the application
2. Delete `~/.iptv_player/iptv_player.db`
3. Restart the application (database will be recreated)
4. Re-add playlists

## Development

### Setting Up Development Environment

1. Clone the repository
2. Create virtual environment: `python -m venv venv`
3. Activate virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`
4. Install dependencies: `pip install -r requirements.txt`
5. Run tests: `python test_app.py`

### Code Style

- Follow PEP 8 guidelines
- Use type hints where possible
- Document all public methods and classes
- Write unit tests for new functionality

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with appropriate tests
4. Submit a pull request

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section
- Review application logs
- Run the test suite to identify issues
- Create an issue in the project repository
